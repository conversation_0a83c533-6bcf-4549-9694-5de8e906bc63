import XCTest
import Foundation
import KeyboardShortcuts
@testable import Nexus

/// Unit tests for ModuleManager
final class ModuleManagerTests: XCTestCase {
    var moduleManager: ModuleManager!
    var configurationManager: ConfigurationManager!
    var mockModule1: MockNexusModule!
    var mockModule2: MockNexusModule!
    
    override func setUp() {
        super.setUp()
        moduleManager = ModuleManager()
        configurationManager = ConfigurationManager()
        
        // Create mock modules for testing
        mockModule1 = MockNexusModule(
            identifier: "test-module-1",
            displayName: "Test Module 1",
            isEnabled: true,
            iconName: "gear"
        )
        
        mockModule2 = MockNexusModule(
            identifier: "test-module-2",
            displayName: "Test Module 2",
            isEnabled: false,
            iconName: "star"
        )
        
        moduleManager.setConfigurationManager(configurationManager)
    }
    
    override func tearDown() {
        moduleManager = nil
        configurationManager = nil
        mockModule1 = nil
        mockModule2 = nil
        super.tearDown()
    }
    
    // MARK: - Module Registration Tests
    
    func testRegisterModule() {
        moduleManager.register(mockModule1)
        
        XCTAssertEqual(moduleManager.allModules.count, 1)
        XCTAssertNotNil(moduleManager.module(for: "test-module-1"))
        XCTAssertEqual(moduleManager.module(for: "test-module-1")?.displayName, "Test Module 1")
    }
    
    func testRegisterMultipleModules() {
        moduleManager.register(mockModule1)
        moduleManager.register(mockModule2)
        
        XCTAssertEqual(moduleManager.allModules.count, 2)
        XCTAssertNotNil(moduleManager.module(for: "test-module-1"))
        XCTAssertNotNil(moduleManager.module(for: "test-module-2"))
    }
    
    func testUnregisterModule() {
        moduleManager.register(mockModule1)
        moduleManager.register(mockModule2)
        
        moduleManager.unregister(identifier: "test-module-1")
        
        XCTAssertEqual(moduleManager.allModules.count, 1)
        XCTAssertNil(moduleManager.module(for: "test-module-1"))
        XCTAssertNotNil(moduleManager.module(for: "test-module-2"))
    }
    
    // MARK: - Module Activation Tests
    
    func testActivateModules() async {
        moduleManager.register(mockModule1)
        moduleManager.register(mockModule2)
        
        await moduleManager.activateModules()
        
        // Only enabled modules should be activated
        XCTAssertTrue(mockModule1.isActivated)
        XCTAssertFalse(mockModule2.isActivated) // This module is disabled
    }
    
    func testDeactivateModules() async {
        moduleManager.register(mockModule1)
        mockModule1.isActivated = true
        
        await moduleManager.deactivateModules()
        
        XCTAssertFalse(mockModule1.isActivated)
    }
    
    // MARK: - Search Tests
    
    func testSearchWithEnabledModules() async {
        moduleManager.register(mockModule1)
        moduleManager.register(mockModule2)
        
        // Set up mock search results
        mockModule1.mockSearchResults = [
            SearchResult(title: "Result 1", subtitle: "From Module 1", iconName: "doc", 
                        moduleIdentifier: "test-module-1", action: .copyToClipboard(text: "test"))
        ]
        
        let results = await moduleManager.search("test query")
        
        // Should only get results from enabled modules
        XCTAssertEqual(results.count, 1)
        XCTAssertEqual(results.first?.title, "Result 1")
        XCTAssertEqual(results.first?.moduleIdentifier, "test-module-1")
    }
    
    func testSearchWithNoEnabledModules() async {
        mockModule1.isEnabled = false
        mockModule2.isEnabled = false
        
        moduleManager.register(mockModule1)
        moduleManager.register(mockModule2)
        
        let results = await moduleManager.search("test query")
        
        XCTAssertEqual(results.count, 0)
    }
    
    func testSearchWithEmptyQuery() async {
        moduleManager.register(mockModule1)
        
        let results = await moduleManager.search("")
        
        XCTAssertEqual(results.count, 0)
    }
    
    // MARK: - Configuration Integration Tests
    
    func testEnabledModulesWithConfiguration() {
        // Register built-in modules
        let calculatorModule = MockNexusModule(
            identifier: "smart-calculator",
            displayName: "Calculator",
            isEnabled: true,
            iconName: "calculator"
        )
        
        moduleManager.register(calculatorModule)
        
        // Initially enabled
        XCTAssertTrue(moduleManager.isModuleEnabled("smart-calculator"))
        
        // Disable via configuration
        configurationManager.toggleModuleEnabled("smart-calculator")
        
        XCTAssertFalse(moduleManager.isModuleEnabled("smart-calculator"))
    }
    
    func testRefreshModules() async {
        moduleManager.register(mockModule1)
        
        // Activate initially
        await moduleManager.activateModules()
        XCTAssertTrue(mockModule1.isActivated)
        
        // Disable module
        mockModule1.isEnabled = false
        
        // Refresh should deactivate and reactivate based on new state
        await moduleManager.refreshModules()
        
        XCTAssertFalse(mockModule1.isActivated)
    }
    
    // MARK: - Keyboard Shortcut Tests
    
    func testHandleKeyboardShortcut() {
        moduleManager.register(mockModule1)
        
        // Mock keyboard shortcut handling
        mockModule1.shouldHandleShortcut = true
        
        let handled = moduleManager.handleKeyboardShortcut(.init("test-shortcut"))
        
        XCTAssertTrue(handled)
        XCTAssertTrue(mockModule1.didReceiveShortcut)
    }
    
    func testHandleKeyboardShortcutNotHandled() {
        moduleManager.register(mockModule1)
        
        mockModule1.shouldHandleShortcut = false
        
        let handled = moduleManager.handleKeyboardShortcut(.init("test-shortcut"))
        
        XCTAssertFalse(handled)
    }
}

// MARK: - Mock Module for Testing

class MockNexusModule: NexusModule {
    let identifier: String
    let displayName: String
    var isEnabled: Bool
    let iconName: String
    
    var isActivated = false
    var mockSearchResults: [SearchResult] = []
    var shouldHandleShortcut = false
    var didReceiveShortcut = false
    
    init(identifier: String, displayName: String, isEnabled: Bool, iconName: String) {
        self.identifier = identifier
        self.displayName = displayName
        self.isEnabled = isEnabled
        self.iconName = iconName
    }
    
    func activate() async throws {
        isActivated = true
    }
    
    func deactivate() async {
        isActivated = false
    }
    
    func search(_ query: String) async -> [SearchResult] {
        guard isEnabled && !query.isEmpty else { return [] }
        return mockSearchResults
    }
    
    func handleKeyboardShortcut(_ shortcut: KeyboardShortcuts.Name) -> Bool {
        didReceiveShortcut = true
        return shouldHandleShortcut
    }
}
