import XCTest
import Foundation
@testable import Nexus

/// Unit tests for SearchViewModel
final class SearchViewModelTests: XCTestCase {
    var searchViewModel: SearchViewModel!
    var moduleManager: ModuleManager!
    var mockModule: MockNexusModule!
    
    override func setUp() {
        super.setUp()
        moduleManager = ModuleManager()
        searchViewModel = SearchViewModel(moduleManager: moduleManager)
        
        // Create and register a mock module
        mockModule = MockNexusModule(
            identifier: "test-module",
            displayName: "Test Module",
            isEnabled: true,
            iconName: "gear"
        )
        moduleManager.register(mockModule)
    }
    
    override func tearDown() {
        searchViewModel = nil
        moduleManager = nil
        mockModule = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testInitialization() {
        XCTAssertEqual(searchViewModel.searchText, "")
        XCTAssertEqual(searchViewModel.searchResults.count, 0)
        XCTAssertFalse(searchViewModel.isSearching)
        XCTAssertEqual(searchViewModel.selectedResultIndex, 0)
    }
    
    // MARK: - Search Tests
    
    func testTriggerSearchWithResults() async {
        // Set up mock search results
        mockModule.mockSearchResults = [
            SearchResult(title: "Test Result 1", subtitle: "Subtitle 1", iconName: "doc",
                        moduleIdentifier: "test-module", action: .copyToClipboard(text: "test1")),
            SearchResult(title: "Test Result 2", subtitle: "Subtitle 2", iconName: "doc",
                        moduleIdentifier: "test-module", action: .copyToClipboard(text: "test2"))
        ]
        
        searchViewModel.searchText = "test query"
        searchViewModel.triggerSearch()
        
        // Wait for async search to complete
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        await MainActor.run {
            XCTAssertEqual(searchViewModel.searchResults.count, 2)
            XCTAssertEqual(searchViewModel.searchResults[0].title, "Test Result 1")
            XCTAssertEqual(searchViewModel.searchResults[1].title, "Test Result 2")
            XCTAssertFalse(searchViewModel.isSearching)
        }
    }
    
    func testTriggerSearchWithEmptyQuery() async {
        searchViewModel.searchText = ""
        searchViewModel.triggerSearch()
        
        // Wait for async operation
        try? await Task.sleep(nanoseconds: 50_000_000) // 0.05 seconds
        
        await MainActor.run {
            XCTAssertEqual(searchViewModel.searchResults.count, 0)
            XCTAssertFalse(searchViewModel.isSearching)
        }
    }
    
    func testSearchingState() async {
        mockModule.mockSearchResults = [
            SearchResult(title: "Test Result", subtitle: "Subtitle", iconName: "doc",
                        moduleIdentifier: "test-module", action: .copyToClipboard(text: "test"))
        ]
        
        searchViewModel.searchText = "test"
        
        // Check that searching state is set during search
        let searchTask = Task {
            searchViewModel.triggerSearch()
        }
        
        // Give it a moment to start
        try? await Task.sleep(nanoseconds: 10_000_000) // 0.01 seconds
        
        await searchTask.value
        
        await MainActor.run {
            XCTAssertFalse(searchViewModel.isSearching) // Should be false after completion
        }
    }
    
    // MARK: - Navigation Tests
    
    func testSelectNext() {
        // Set up some results
        searchViewModel.searchResults = [
            SearchResult(title: "Result 1", subtitle: nil, iconName: "doc",
                        moduleIdentifier: "test", action: .copyToClipboard(text: "1")),
            SearchResult(title: "Result 2", subtitle: nil, iconName: "doc",
                        moduleIdentifier: "test", action: .copyToClipboard(text: "2")),
            SearchResult(title: "Result 3", subtitle: nil, iconName: "doc",
                        moduleIdentifier: "test", action: .copyToClipboard(text: "3"))
        ]
        
        XCTAssertEqual(searchViewModel.selectedResultIndex, 0)
        
        searchViewModel.selectNext()
        XCTAssertEqual(searchViewModel.selectedResultIndex, 1)
        
        searchViewModel.selectNext()
        XCTAssertEqual(searchViewModel.selectedResultIndex, 2)
        
        // Should not go beyond last index
        searchViewModel.selectNext()
        XCTAssertEqual(searchViewModel.selectedResultIndex, 2)
    }
    
    func testSelectPrevious() {
        // Set up some results
        searchViewModel.searchResults = [
            SearchResult(title: "Result 1", subtitle: nil, iconName: "doc",
                        moduleIdentifier: "test", action: .copyToClipboard(text: "1")),
            SearchResult(title: "Result 2", subtitle: nil, iconName: "doc",
                        moduleIdentifier: "test", action: .copyToClipboard(text: "2")),
            SearchResult(title: "Result 3", subtitle: nil, iconName: "doc",
                        moduleIdentifier: "test", action: .copyToClipboard(text: "3"))
        ]
        
        // Start at index 2
        searchViewModel.selectedResultIndex = 2
        
        searchViewModel.selectPrevious()
        XCTAssertEqual(searchViewModel.selectedResultIndex, 1)
        
        searchViewModel.selectPrevious()
        XCTAssertEqual(searchViewModel.selectedResultIndex, 0)
        
        // Should not go below 0
        searchViewModel.selectPrevious()
        XCTAssertEqual(searchViewModel.selectedResultIndex, 0)
    }
    
    // MARK: - Action Execution Tests
    
    func testExecuteSelectedResult() async {
        // Set up a result
        let testResult = SearchResult(
            title: "Test Result",
            subtitle: nil,
            iconName: "doc",
            moduleIdentifier: "test",
            action: .copyToClipboard(text: "test content")
        )
        
        searchViewModel.searchResults = [testResult]
        searchViewModel.selectedResultIndex = 0
        
        await searchViewModel.executeSelectedResult()
        
        // Verify clipboard content
        let pasteboard = NSPasteboard.general
        XCTAssertEqual(pasteboard.string(forType: .string), "test content")
        
        // Verify search is cleared
        await MainActor.run {
            XCTAssertEqual(searchViewModel.searchText, "")
            XCTAssertEqual(searchViewModel.searchResults.count, 0)
        }
    }
    
    func testExecuteResultWithInvalidIndex() async {
        searchViewModel.searchResults = []
        searchViewModel.selectedResultIndex = 0
        
        // Should not crash with invalid index
        await searchViewModel.executeSelectedResult()
        
        await MainActor.run {
            XCTAssertEqual(searchViewModel.searchResults.count, 0)
        }
    }
    
    func testExecuteCalculateAction() async {
        let calculateResult = SearchResult(
            title: "42",
            subtitle: "2 + 2 * 20",
            iconName: "calculator",
            moduleIdentifier: "calculator",
            action: .calculate(expression: "42")
        )
        
        await searchViewModel.executeResult(calculateResult)
        
        // Verify result is copied to clipboard
        let pasteboard = NSPasteboard.general
        XCTAssertEqual(pasteboard.string(forType: .string), "42")
    }
    
    func testExecuteLaunchApplicationAction() async {
        let launchResult = SearchResult(
            title: "Calculator",
            subtitle: "System Calculator",
            iconName: "calculator",
            moduleIdentifier: "launcher",
            action: .launchApplication(path: "/System/Applications/Calculator.app")
        )
        
        // This test just verifies the action doesn't crash
        // We can't easily test actual app launching in unit tests
        await searchViewModel.executeResult(launchResult)
        
        await MainActor.run {
            XCTAssertEqual(searchViewModel.searchText, "")
            XCTAssertEqual(searchViewModel.searchResults.count, 0)
        }
    }
    
    // MARK: - Clear Search Tests
    
    func testClearSearch() {
        // Set up some state
        searchViewModel.searchText = "test query"
        searchViewModel.searchResults = [
            SearchResult(title: "Result", subtitle: nil, iconName: "doc",
                        moduleIdentifier: "test", action: .copyToClipboard(text: "test"))
        ]
        searchViewModel.selectedResultIndex = 1
        
        searchViewModel.clearSearch()
        
        XCTAssertEqual(searchViewModel.searchText, "")
        XCTAssertEqual(searchViewModel.searchResults.count, 0)
        XCTAssertEqual(searchViewModel.selectedResultIndex, 0)
    }
}
