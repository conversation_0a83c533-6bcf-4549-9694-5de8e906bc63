import XCTest
import Foundation
@testable import Nexus

/// Integration tests for DataService
final class DataServiceTests: XCTestCase {
    var dataService: DataService!
    var securityManager: SecurityManager!
    var tempDirectory: URL!
    var databaseManager: DatabaseManager!

    override func setUp() {
        super.setUp()

        // Create temporary directory for test database
        tempDirectory = FileManager.default.temporaryDirectory
            .appendingPathComponent("NexusDataServiceTests")
            .appendingPathComponent(UUID().uuidString)

        try! FileManager.default.createDirectory(at: tempDirectory,
                                               withIntermediateDirectories: true)

        // Initialize database manager with test database path
        let testDbPath = tempDirectory.appendingPathComponent("test.db").path
        databaseManager = DatabaseManager(databasePath: testDbPath)

        // Initialize data service with test database manager
        dataService = DataService(databaseManager: databaseManager)
        
        // Set up security manager for encryption tests
        securityManager = SecurityManager()
        let securitySettings = SecuritySettings(
            enableDataEncryption: true,
            enableClipboardEncryption: true,
            enableNotesEncryption: true,
            dataRetentionDays: 30,
            autoLockAfterMinutes: 0,
            requireAuthenticationOnLaunch: false,
            enableSecureErase: true,
            allowDataExport: true,
            enableAuditLogging: true
        )
        securityManager.configure(with: securitySettings)
    }
    
    override func tearDown() {
        // Clean up temporary directory
        try? FileManager.default.removeItem(at: tempDirectory)
        dataService = nil
        databaseManager = nil
        securityManager = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testInitialization() {
        XCTAssertNotNil(dataService)
        XCTAssertEqual(dataService.notes.count, 0)
        XCTAssertEqual(dataService.folders.count, 0)
        XCTAssertFalse(dataService.isLoading)
        XCTAssertNil(dataService.error)
    }
    
    // MARK: - Note Management Tests
    
    func testCreateNote() async {
        let note = dataService.createNote(content: "Test note content")
        
        XCTAssertEqual(note.content, "Test note content")
        XCTAssertTrue(note.title.contains("Test note")) // Auto-generated title
        
        // Wait for async database operation
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        await MainActor.run {
            XCTAssertEqual(dataService.notes.count, 1)
            XCTAssertEqual(dataService.notes.first?.id, note.id)
        }
    }
    
    func testCreateEmptyNote() async {
        let note = dataService.createNote()
        
        XCTAssertEqual(note.content, "")
        XCTAssertEqual(note.title, "Untitled Note")
        
        // Wait for async database operation
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        await MainActor.run {
            XCTAssertEqual(dataService.notes.count, 1)
        }
    }
    
    func testUpdateNote() async {
        let originalNote = dataService.createNote(content: "Original content")
        
        // Wait for creation
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        let updatedNote = Note(
            id: originalNote.id,
            content: "Updated content",
            title: "Updated Title",
            createdAt: originalNote.createdAt,
            modifiedAt: Date(),
            isFavorite: true,
            isArchived: false
        )
        
        await dataService.updateNote(updatedNote)
        
        // Wait for update
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        await MainActor.run {
            let updatedNoteInService = dataService.notes.first { $0.id == originalNote.id }
            XCTAssertNotNil(updatedNoteInService)
            XCTAssertEqual(updatedNoteInService?.content, "Updated content")
            XCTAssertEqual(updatedNoteInService?.title, "Updated Title")
            XCTAssertTrue(updatedNoteInService?.isFavorite ?? false)
        }
    }
    
    func testDeleteNote() async {
        let note = dataService.createNote(content: "Note to delete")
        
        // Wait for creation
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        await MainActor.run {
            XCTAssertEqual(dataService.notes.count, 1)
        }
        
        await dataService.deleteNote(note)
        
        // Wait for deletion
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        await MainActor.run {
            XCTAssertEqual(dataService.notes.count, 0)
        }
    }
    
    func testToggleNoteFavorite() async {
        let note = dataService.createNote(content: "Test note")
        
        // Wait for creation
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        XCTAssertFalse(note.isFavorite)
        
        dataService.toggleFavorite(note)

        // Wait for async update to complete
        try? await Task.sleep(nanoseconds: 200_000_000)

        await MainActor.run {
            let updatedNote = dataService.notes.first { $0.id == note.id }
            XCTAssertTrue(updatedNote?.isFavorite ?? false)
        }

        // Toggle back
        dataService.toggleFavorite(note)

        // Wait for async update to complete
        try? await Task.sleep(nanoseconds: 200_000_000)

        await MainActor.run {
            let updatedNote = dataService.notes.first { $0.id == note.id }
            XCTAssertFalse(updatedNote?.isFavorite ?? true)
        }
    }
    
    func testToggleNoteArchived() async {
        let note = dataService.createNote(content: "Test note")
        
        // Wait for creation
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        XCTAssertFalse(note.isArchived)
        
        dataService.toggleArchive(note)

        // Wait for async update to complete
        try? await Task.sleep(nanoseconds: 200_000_000)

        await MainActor.run {
            let updatedNote = dataService.notes.first { $0.id == note.id }
            XCTAssertTrue(updatedNote?.isArchived ?? false)
        }
    }
    
    // MARK: - Folder Management Tests
    
    func testCreateFolder() async {
        let folder = await dataService.createFolder(name: "Test Folder", color: "#FF0000")
        
        XCTAssertEqual(folder.name, "Test Folder")
        XCTAssertEqual(folder.color, "#FF0000")
        
        // Wait for async database operation
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        await MainActor.run {
            XCTAssertEqual(dataService.folders.count, 1)
            XCTAssertEqual(dataService.folders.first?.id, folder.id)
        }
    }
    
    // TODO: Implement updateFolder method in DataService
    /*
    func testUpdateFolder() async {
        let originalFolder = await dataService.createFolder(name: "Original Name", color: "#FF0000")

        // Wait for creation
        try? await Task.sleep(nanoseconds: 100_000_000)

        let updatedFolder = Folder(
            id: originalFolder.id,
            name: "Updated Name",
            color: "#00FF00",
            createdAt: originalFolder.createdAt,
            parentId: nil
        )

        await dataService.updateFolder(updatedFolder)

        // Wait for update
        try? await Task.sleep(nanoseconds: 100_000_000)

        await MainActor.run {
            let updatedFolderInService = dataService.folders.first { $0.id == originalFolder.id }
            XCTAssertNotNil(updatedFolderInService)
            XCTAssertEqual(updatedFolderInService?.name, "Updated Name")
            XCTAssertEqual(updatedFolderInService?.color, "#00FF00")
        }
    }
    */
    
    // TODO: Implement deleteFolder method in DataService
    /*
    func testDeleteFolder() async {
        let folder = await dataService.createFolder(name: "Folder to delete", color: "#FF0000")

        // Wait for creation
        try? await Task.sleep(nanoseconds: 100_000_000)

        await MainActor.run {
            XCTAssertEqual(dataService.folders.count, 1)
        }

        await dataService.deleteFolder(folder.id)

        // Wait for deletion
        try? await Task.sleep(nanoseconds: 100_000_000)

        await MainActor.run {
            XCTAssertEqual(dataService.folders.count, 0)
        }
    }
    */
    
    // MARK: - Search Tests
    
    func testSearchNotes() async {
        let note1 = dataService.createNote(content: "Swift programming tutorial")
        let note2 = dataService.createNote(content: "Python data science")
        let note3 = dataService.createNote(content: "JavaScript web development")
        
        // Wait for creation
        try? await Task.sleep(nanoseconds: 200_000_000)
        
        let swiftResults = await dataService.searchNotes(query: "Swift")
        XCTAssertEqual(swiftResults.count, 1)
        XCTAssertEqual(swiftResults.first?.id, note1.id)

        let programmingResults = await dataService.searchNotes(query: "programming")
        XCTAssertEqual(programmingResults.count, 1)

        let developmentResults = await dataService.searchNotes(query: "development")
        XCTAssertEqual(developmentResults.count, 1)
        XCTAssertEqual(developmentResults.first?.id, note3.id)
    }
    
    func testSearchNotesNoResults() async {
        dataService.createNote(content: "Test note")
        
        // Wait for creation
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        let results = await dataService.searchNotes(query: "nonexistent")
        XCTAssertEqual(results.count, 0)
    }
    
    // MARK: - Security Integration Tests
    
    func testConfigureWithSecurityManager() async {
        dataService.configure(securityManager: securityManager)
        
        // Wait for configuration
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        // Create a note with encryption enabled
        let note = dataService.createNote(content: "Encrypted note content")
        
        // Wait for creation
        try? await Task.sleep(nanoseconds: 100_000_000)
        
        await MainActor.run {
            XCTAssertEqual(dataService.notes.count, 1)
            XCTAssertEqual(dataService.notes.first?.content, "Encrypted note content")
        }
    }
    
    // MARK: - Error Handling Tests
    
    func testErrorHandling() async {
        // This test verifies that errors are properly captured
        // In a real scenario, we might simulate database errors
        
        await MainActor.run {
            XCTAssertNil(dataService.error)
        }
        
        // After any operation that might fail, error should be captured
        // For now, we just verify the error property exists and can be set
    }
    
    // MARK: - Loading State Tests
    
    func testLoadingState() async {
        // Initially not loading (after setup)
        await MainActor.run {
            XCTAssertFalse(dataService.isLoading)
        }
        
        // Create a note and check loading state briefly
        dataService.createNote(content: "Test note")
        
        // Loading state is typically very brief, so we just verify
        // it eventually returns to false
        try? await Task.sleep(nanoseconds: 200_000_000)
        
        await MainActor.run {
            XCTAssertFalse(dataService.isLoading)
        }
    }
}
