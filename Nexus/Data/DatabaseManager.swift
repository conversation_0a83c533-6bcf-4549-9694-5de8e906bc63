import Foundation
import SQLite

/// Manages SQLite database operations for Nexus
class DatabaseManager {
    private var db: Connection?
    private let dbPath: String
    private var securityManager: SecurityManager?
    
    // Table definitions
    private let notes = Table("notes")
    private let folders = Table("folders")
    private let noteFolders = Table("note_folders")
    
    // Notes table columns
    private let noteId = Expression<String>("id")
    private let noteContent = Expression<String>("content")
    private let noteTitle = Expression<String>("title")
    private let noteCreatedAt = Expression<Date>("created_at")
    private let noteModifiedAt = Expression<Date>("modified_at")
    private let noteIsFavorite = Expression<Bool>("is_favorite")
    private let noteIsArchived = Expression<Bool>("is_archived")
    
    // Folders table columns
    private let folderId = Expression<String>("id")
    private let folderName = Expression<String>("name")
    private let folderColor = Expression<String?>("color")
    private let folderCreatedAt = Expression<Date>("created_at")
    private let folderParentId = Expression<String?>("parent_id")
    
    // Note-Folders junction table columns
    private let junctionNoteId = Expression<String>("note_id")
    private let junctionFolderId = Expression<String>("folder_id")
    
    init() {
        // Get application support directory
        let fileManager = FileManager.default
        let appSupportURL = fileManager.urls(for: .applicationSupportDirectory,
                                           in: .userDomainMask).first!
        let nexusDirectory = appSupportURL.appendingPathComponent("Nexus")

        // Create directory if it doesn't exist
        try? fileManager.createDirectory(at: nexusDirectory,
                                       withIntermediateDirectories: true)

        dbPath = nexusDirectory.appendingPathComponent("nexus.db").path

        initializeDatabase()
    }

    /// Initialize with custom database path (for testing)
    init(databasePath: String) {
        dbPath = databasePath
        initializeDatabase()
    }

    /// Common database initialization
    private func initializeDatabase() {
        do {
            db = try Connection(dbPath)
            createTables()
            enableFTS()
        } catch {
            print("Database initialization failed: \(error)")
        }
    }
    
    /// Create database tables
    private func createTables() {
        do {
            // Create notes table
            try db?.run(notes.create(ifNotExists: true) { t in
                t.column(noteId, primaryKey: true)
                t.column(noteContent)
                t.column(noteTitle)
                t.column(noteCreatedAt)
                t.column(noteModifiedAt)
                t.column(noteIsFavorite, defaultValue: false)
                t.column(noteIsArchived, defaultValue: false)
            })
            
            // Create folders table
            try db?.run(folders.create(ifNotExists: true) { t in
                t.column(folderId, primaryKey: true)
                t.column(folderName)
                t.column(folderColor)
                t.column(folderCreatedAt)
                t.column(folderParentId)
                t.foreignKey(folderParentId, references: folders, folderId, delete: .cascade)
            })
            
            // Create note-folders junction table
            try db?.run(noteFolders.create(ifNotExists: true) { t in
                t.column(junctionNoteId)
                t.column(junctionFolderId)
                t.primaryKey(junctionNoteId, junctionFolderId)
                t.foreignKey(junctionNoteId, references: notes, noteId, delete: .cascade)
                t.foreignKey(junctionFolderId, references: folders, folderId, delete: .cascade)
            })
            
        } catch {
            print("Failed to create tables: \(error)")
        }
    }
    
    /// Enable Full-Text Search (FTS5)
    private func enableFTS() {
        do {
            // Create FTS virtual table for notes
            try db?.execute("""
                CREATE VIRTUAL TABLE IF NOT EXISTS notes_fts USING fts5(
                    id UNINDEXED,
                    title,
                    content,
                    content='notes',
                    content_rowid='rowid'
                );
            """)
            
            // Create triggers to keep FTS table in sync
            try db?.execute("""
                CREATE TRIGGER IF NOT EXISTS notes_fts_insert AFTER INSERT ON notes BEGIN
                    INSERT INTO notes_fts(id, title, content) VALUES (new.id, new.title, new.content);
                END;
            """)
            
            try db?.execute("""
                CREATE TRIGGER IF NOT EXISTS notes_fts_delete AFTER DELETE ON notes BEGIN
                    DELETE FROM notes_fts WHERE id = old.id;
                END;
            """)
            
            try db?.execute("""
                CREATE TRIGGER IF NOT EXISTS notes_fts_update AFTER UPDATE ON notes BEGIN
                    DELETE FROM notes_fts WHERE id = old.id;
                    INSERT INTO notes_fts(id, title, content) VALUES (new.id, new.title, new.content);
                END;
            """)
            
        } catch {
            print("Failed to setup FTS: \(error)")
        }
    }

    /// Configure the database manager with security manager
    func configure(securityManager: SecurityManager) {
        self.securityManager = securityManager
    }

    /// Insert a new note
    func insertNote(_ note: Note) throws {
        guard let db = db else { throw DatabaseError.connectionFailed }

        // Encrypt content and title if security manager is available
        let contentToStore: String
        let titleToStore: String

        if let securityManager = securityManager {
            // Store encrypted data as base64 string
            let encryptedContent = try securityManager.encryptNoteContent(note.content)
            let encryptedTitle = try securityManager.encryptNoteContent(note.title)
            contentToStore = encryptedContent.base64EncodedString()
            titleToStore = encryptedTitle.base64EncodedString()
        } else {
            contentToStore = note.content
            titleToStore = note.title
        }

        let insert = notes.insert(
            noteId <- note.id.uuidString,
            noteContent <- contentToStore,
            noteTitle <- titleToStore,
            noteCreatedAt <- note.createdAt,
            noteModifiedAt <- note.modifiedAt,
            noteIsFavorite <- note.isFavorite,
            noteIsArchived <- note.isArchived
        )

        try db.run(insert)
    }
    
    /// Update an existing note
    func updateNote(_ note: Note) throws {
        guard let db = db else { throw DatabaseError.connectionFailed }

        // Encrypt content and title if security manager is available
        let contentToStore: String
        let titleToStore: String

        if let securityManager = securityManager {
            // Store encrypted data as base64 string
            let encryptedContent = try securityManager.encryptNoteContent(note.content)
            let encryptedTitle = try securityManager.encryptNoteContent(note.title)
            contentToStore = encryptedContent.base64EncodedString()
            titleToStore = encryptedTitle.base64EncodedString()
        } else {
            contentToStore = note.content
            titleToStore = note.title
        }

        let noteToUpdate = notes.filter(noteId == note.id.uuidString)
        let update = noteToUpdate.update(
            noteContent <- contentToStore,
            noteTitle <- titleToStore,
            noteModifiedAt <- note.modifiedAt,
            noteIsFavorite <- note.isFavorite,
            noteIsArchived <- note.isArchived
        )

        try db.run(update)
    }
    
    /// Delete a note
    func deleteNote(id: UUID) throws {
        guard let db = db else { throw DatabaseError.connectionFailed }
        
        let noteToDelete = notes.filter(noteId == id.uuidString)
        try db.run(noteToDelete.delete())
    }
    
    /// Get all notes
    func getAllNotes() throws -> [Note] {
        guard let db = db else { throw DatabaseError.connectionFailed }

        var notesList: [Note] = []

        for row in try db.prepare(notes.order(noteModifiedAt.desc)) {
            // Decrypt content and title if security manager is available
            let content: String
            let title: String

            if let securityManager = securityManager {
                // Decrypt data from base64 string
                if let contentData = Data(base64Encoded: row[noteContent]) {
                    content = try securityManager.decryptNoteContent(contentData)
                } else {
                    content = row[noteContent] // Fallback for unencrypted data
                }

                if let titleData = Data(base64Encoded: row[noteTitle]) {
                    title = try securityManager.decryptNoteContent(titleData)
                } else {
                    title = row[noteTitle] // Fallback for unencrypted data
                }
            } else {
                content = row[noteContent]
                title = row[noteTitle]
            }

            let note = Note(
                id: UUID(uuidString: row[noteId])!,
                content: content,
                title: title,
                createdAt: row[noteCreatedAt],
                modifiedAt: row[noteModifiedAt],
                isFavorite: row[noteIsFavorite],
                isArchived: row[noteIsArchived]
            )
            notesList.append(note)
        }

        return notesList
    }
    
    /// Search notes using FTS
    func searchNotes(query: String) throws -> [Note] {
        guard let db = db else { throw DatabaseError.connectionFailed }
        guard !query.isEmpty else { return try getAllNotes() }
        
        var notesList: [Note] = []
        
        let searchQuery = """
            SELECT n.* FROM notes n
            JOIN notes_fts fts ON n.id = fts.id
            WHERE notes_fts MATCH ?
            ORDER BY rank
        """
        
        for row in try db.prepare(searchQuery, query) {
            let note = Note(
                id: UUID(uuidString: row[0] as! String)!,
                content: row[1] as! String,
                title: row[2] as! String,
                createdAt: row[3] as! Date,
                modifiedAt: row[4] as! Date,
                isFavorite: row[5] as! Bool,
                isArchived: row[6] as! Bool
            )
            notesList.append(note)
        }
        
        return notesList
    }
    
    /// Insert a new folder
    func insertFolder(_ folder: Folder) throws {
        guard let db = db else { throw DatabaseError.connectionFailed }
        
        let insert = folders.insert(
            folderId <- folder.id.uuidString,
            folderName <- folder.name,
            folderColor <- folder.color,
            folderCreatedAt <- folder.createdAt,
            folderParentId <- folder.parentId?.uuidString
        )
        
        try db.run(insert)
    }
    
    /// Get all folders
    func getAllFolders() throws -> [Folder] {
        guard let db = db else { throw DatabaseError.connectionFailed }
        
        var foldersList: [Folder] = []
        
        for row in try db.prepare(folders.order(folderName.asc)) {
            let folder = Folder(
                id: UUID(uuidString: row[folderId])!,
                name: row[folderName],
                color: row[folderColor],
                createdAt: row[folderCreatedAt],
                parentId: row[folderParentId].map { UUID(uuidString: $0)! }
            )
            foldersList.append(folder)
        }
        
        return foldersList
    }
}

/// Database-related errors
enum DatabaseError: Error {
    case connectionFailed
    case queryFailed
    case invalidData
}
