import Foundation

/// High-level data service that manages notes and folders
@Observable
class DataService {
    private let databaseManager: DatabaseManager
    
    var notes: [Note] = []
    var folders: [Folder] = []
    var isLoading = false
    var error: Error?
    
    init() {
        self.databaseManager = DatabaseManager()
        loadData()
    }

    /// Initialize with custom database manager (for testing)
    init(databaseManager: DatabaseManager) {
        self.databaseManager = databaseManager
        loadData()
    }

    /// Configure the data service with security manager
    func configure(securityManager: SecurityManager) {
        databaseManager.configure(securityManager: securityManager)
        // Reload data to apply encryption/decryption
        loadData()
    }
    
    /// Load all data from database
    func loadData() {
        isLoading = true
        error = nil
        
        Task {
            do {
                let loadedNotes = try databaseManager.getAllNotes()
                let loadedFolders = try databaseManager.getAllFolders()
                
                await MainActor.run {
                    self.notes = loadedNotes
                    self.folders = loadedFolders
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.error = error
                    self.isLoading = false
                }
            }
        }
    }
    
    /// Create a new note
    func createNote(content: String = "") -> Note {
        let note = Note(content: content)
        
        Task {
            do {
                try databaseManager.insertNote(note)
                await MainActor.run {
                    self.notes.insert(note, at: 0)
                }
            } catch {
                await MainActor.run {
                    self.error = error
                }
            }
        }
        
        return note
    }
    
    /// Update an existing note
    func updateNote(_ note: Note) {
        Task {
            do {
                try databaseManager.updateNote(note)
                await MainActor.run {
                    if let index = self.notes.firstIndex(where: { $0.id == note.id }) {
                        self.notes[index] = note
                        // Move to top if content changed
                        if index != 0 {
                            self.notes.remove(at: index)
                            self.notes.insert(note, at: 0)
                        }
                    }
                }
            } catch {
                await MainActor.run {
                    self.error = error
                }
            }
        }
    }
    
    /// Delete a note
    func deleteNote(_ note: Note) {
        Task {
            do {
                try databaseManager.deleteNote(id: note.id)
                await MainActor.run {
                    self.notes.removeAll { $0.id == note.id }
                }
            } catch {
                await MainActor.run {
                    self.error = error
                }
            }
        }
    }
    
    /// Search notes
    func searchNotes(query: String) async -> [Note] {
        do {
            return try databaseManager.searchNotes(query: query)
        } catch {
            await MainActor.run {
                self.error = error
            }
            return []
        }
    }
    
    /// Create a new folder
    func createFolder(name: String, color: String? = nil, parentId: UUID? = nil) -> Folder {
        let folder = Folder(name: name, color: color, parentId: parentId)
        
        Task {
            do {
                try databaseManager.insertFolder(folder)
                await MainActor.run {
                    self.folders.append(folder)
                    self.folders.sort { $0.name < $1.name }
                }
            } catch {
                await MainActor.run {
                    self.error = error
                }
            }
        }
        
        return folder
    }
    
    /// Get notes in a specific folder
    func getNotesInFolder(_ folderId: UUID) -> [Note] {
        // This would require implementing the junction table queries
        // For now, return all notes (to be implemented with folder associations)
        return notes
    }
    
    /// Get recent notes (last 10)
    func getRecentNotes() -> [Note] {
        return Array(notes.prefix(10))
    }
    
    /// Get favorite notes
    func getFavoriteNotes() -> [Note] {
        return notes.filter { $0.isFavorite }
    }
    
    /// Get archived notes
    func getArchivedNotes() -> [Note] {
        return notes.filter { $0.isArchived }
    }
    
    /// Toggle favorite status of a note
    func toggleFavorite(_ note: Note) {
        var updatedNote = note
        updatedNote.isFavorite.toggle()
        updateNote(updatedNote)
    }
    
    /// Toggle archive status of a note
    func toggleArchive(_ note: Note) {
        var updatedNote = note
        updatedNote.isArchived.toggle()
        updateNote(updatedNote)
    }
    
    /// Get folder hierarchy (root folders first, then children)
    func getFolderHierarchy() -> [Folder] {
        let rootFolders = folders.filter { $0.parentId == nil }
        var hierarchy: [Folder] = []
        
        func addFolderAndChildren(_ folder: Folder, level: Int = 0) {
            hierarchy.append(folder)
            let children = folders.filter { $0.parentId == folder.id }
            for child in children.sorted(by: { $0.name < $1.name }) {
                addFolderAndChildren(child, level: level + 1)
            }
        }
        
        for rootFolder in rootFolders.sorted(by: { $0.name < $1.name }) {
            addFolderAndChildren(rootFolder)
        }
        
        return hierarchy
    }
    
    /// Export note as text file
    func exportNote(_ note: Note) -> URL? {
        let fileName = "\(note.title).txt"
        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
        
        do {
            try note.content.write(to: tempURL, atomically: true, encoding: .utf8)
            return tempURL
        } catch {
            self.error = error
            return nil
        }
    }
    
    /// Import note from text file
    func importNote(from url: URL) -> Note? {
        do {
            let content = try String(contentsOf: url, encoding: .utf8)
            let note = createNote(content: content)
            return note
        } catch {
            self.error = error
            return nil
        }
    }
    
    /// Get statistics
    func getStatistics() -> (totalNotes: Int, favoriteNotes: Int, archivedNotes: Int, totalFolders: Int) {
        return (
            totalNotes: notes.count,
            favoriteNotes: notes.filter { $0.isFavorite }.count,
            archivedNotes: notes.filter { $0.isArchived }.count,
            totalFolders: folders.count
        )
    }
}
